{"startTime": "2025-09-12T17:31:20.031Z", "entries": [{"requestId": "daecab5c-38f1-4569-8099-30063f95802e", "service": "cline.AccountService", "method": "accountLoginClicked", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"value": "http://localhost:7777/"}}, "duration": 26}, {"requestId": "26afbaa5-05e1-431f-8d45-5281ae7d0ee7", "service": "cline.AccountService", "method": "getUserOrganizations", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"organizations": [{"active": false, "memberId": "random-member-id", "name": "Test Organization", "organizationId": "random-org-id", "roles": ["member"]}]}}, "duration": 2}, {"requestId": "118bb19e-50b4-431a-a10e-df5b1570f988", "service": "cline.TaskService", "method": "newTask", "isStreaming": false, "request": {"message": {"text": "Hello, <PERSON><PERSON>!", "images": [], "files": []}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 53}, {"requestId": "275c8b4f-ed27-4c13-8ddf-c1ce67307d32", "service": "cline.TaskService", "method": "clearTask", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 4}, {"requestId": "879968bf-bfcb-44d6-be27-16abc7b8d73c", "service": "cline.TaskService", "method": "newTask", "isStreaming": false, "request": {"message": {"text": "edit_request", "images": [], "files": []}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 47}, {"requestId": "879968bf-bfcb-44d6-be27-16abc7b8d73c", "service": "cline.StateService", "method": "getLatestState", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": true}, "response": {"message": {"stateJson": "{\"version\":\"3.28.0\",\"apiConfiguration\":{\"openAiHeaders\":{},\"planModeApiProvider\":\"cline\",\"planModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\",\"actModeApiProvider\":\"cline\",\"actModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\"},\"uriScheme\":\"vscode\",\"currentTaskItem\":{\"id\":\"*************\",\"ulid\":\"01K4ZFPMKPY3V7WC2Q6V92KR6K\",\"ts\":*************,\"task\":\"edit_request\",\"tokensIn\":0,\"tokensOut\":0,\"totalCost\":0,\"size\":608,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false},\"clineMessages\":[{\"ts\":*************,\"type\":\"say\",\"say\":\"text\",\"text\":\"edit_request\",\"images\":[],\"files\":[],\"conversationHistoryIndex\":-1},{\"ts\":*************,\"type\":\"say\",\"say\":\"api_req_started\",\"text\":\"{\\\"request\\\":\\\"<task>\\\\nedit_request\\\\n</task>\\\\n\\\\nLoading...\\\"}\",\"conversationHistoryIndex\":-1}],\"currentFocusChainChecklist\":null,\"autoApprovalSettings\":{\"version\":3,\"enabled\":true,\"actions\":{\"readFiles\":true,\"readFilesExternally\":false,\"editFiles\":false,\"editFilesExternally\":false,\"executeSafeCommands\":true,\"executeAllCommands\":false,\"useBrowser\":false,\"useMcp\":false},\"maxRequests\":20,\"enableNotifications\":false,\"favorites\":[\"enableAutoApprove\",\"readFiles\",\"editFiles\"]},\"browserSettings\":{\"viewport\":{\"width\":900,\"height\":600},\"remoteBrowserEnabled\":false,\"remoteBrowserHost\":\"http://localhost:9222\",\"chromeExecutablePath\":\"\",\"disableToolUse\":false,\"customArgs\":\"\"},\"focusChainSettings\":{\"enabled\":true,\"remindClineInterval\":6},\"focusChainFeatureFlagEnabled\":false,\"preferredLanguage\":\"English\",\"openaiReasoningEffort\":\"medium\",\"mode\":\"act\",\"strictPlanModeEnabled\":true,\"useAutoCondense\":false,\"mcpMarketplaceEnabled\":true,\"mcpDisplayMode\":\"plain\",\"telemetrySetting\":\"unset\",\"planActSeparateModelsSetting\":false,\"enableCheckpointsSetting\":true,\"distinctId\":\"test-member-789\",\"globalClineRulesToggles\":{},\"localClineRulesToggles\":{},\"localWindsurfRulesToggles\":{},\"localCursorRulesToggles\":{},\"localWorkflowToggles\":{},\"globalWorkflowToggles\":{},\"shellIntegrationTimeout\":4000,\"terminalReuseEnabled\":true,\"defaultTerminalProfile\":\"default\",\"isNewUser\":true,\"welcomeViewCompleted\":true,\"mcpResponsesCollapsed\":false,\"terminalOutputLineLimit\":500,\"taskHistory\":[{\"id\":\"*************\",\"ulid\":\"01K4ZFPMKPY3V7WC2Q6V92KR6K\",\"ts\":*************,\"task\":\"edit_request\",\"tokensIn\":0,\"tokensOut\":0,\"totalCost\":0,\"size\":608,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false},{\"id\":\"1757698280645\",\"ulid\":\"01K4ZFPK65H5Q4YZT73Q6YK7SQ\",\"ts\":1757698281524,\"task\":\"Hello, Cline!\",\"tokensIn\":420,\"tokensOut\":273,\"cacheWrites\":0,\"cacheReads\":0,\"totalCost\":0.10395,\"size\":9379,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false}],\"platform\":\"darwin\",\"shouldShowAnnouncement\":false,\"extensionInfo\":{\"name\":\"claude-dev\",\"publisher\":\"saoudrizwan\"},\"workspaceRoots\":[{\"path\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"name\":\"workspace\",\"vcs\":\"git\",\"commitHash\":\"0ac634924409b1ac2b125e1d03be4abc91455161\"}],\"primaryRootIndex\":0,\"isMultiRootWorkspace\":false}"}}, "duration": 0}], "stats": {"totalRequests": 6, "pendingRequests": 0, "completedRequests": 6, "errorRequests": 0}}