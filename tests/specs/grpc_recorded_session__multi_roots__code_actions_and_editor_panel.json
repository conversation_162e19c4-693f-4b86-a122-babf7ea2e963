{"startTime": "2025-09-12T17:32:20.699Z", "entries": [{"requestId": "690d345f-c9cc-4157-b056-1ac3a6803530", "service": "cline.AccountService", "method": "accountLoginClicked", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"value": "http://localhost:7777/"}}, "duration": 13}, {"requestId": "8af1f088-dfe0-469a-a435-c66dbc78f23f", "service": "cline.AccountService", "method": "getUserOrganizations", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"organizations": [{"active": false, "memberId": "random-member-id", "name": "Test Organization", "organizationId": "random-org-id", "roles": ["member"]}]}}, "duration": 3}, {"requestId": "2b0db7ed-ab19-465e-8634-d99be17f0dc0", "service": "cline.AccountService", "method": "getUserOrganizations", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"organizations": [{"active": false, "memberId": "random-member-id", "name": "Test Organization", "organizationId": "random-org-id", "roles": ["member"]}]}}, "duration": 3}, {"requestId": "9e24b19d-81c9-4405-a1c4-29862797f34f", "service": "cline.AccountService", "method": "getUserOrganizations", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"organizations": [{"active": false, "memberId": "random-member-id", "name": "Test Organization", "organizationId": "random-org-id", "roles": ["member"]}]}}, "duration": 9}, {"requestId": "806afa5a-ece5-48b3-87fc-2d842cd4bc5f", "service": "cline.AccountService", "method": "getUserOrganizations", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"organizations": [{"active": false, "memberId": "random-member-id", "name": "Test Organization", "organizationId": "random-org-id", "roles": ["member"]}]}}, "duration": 8}, {"requestId": "9e24b19d-81c9-4405-a1c4-29862797f34f", "service": "cline.StateService", "method": "getLatestState", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": true}, "response": {"message": {"stateJson": "{\"version\":\"3.28.0\",\"apiConfiguration\":{\"openAiHeaders\":{},\"planModeApiProvider\":\"cline\",\"planModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\",\"actModeApiProvider\":\"cline\",\"actModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\"},\"uriScheme\":\"vscode\",\"clineMessages\":[],\"currentFocusChainChecklist\":null,\"autoApprovalSettings\":{\"version\":1,\"enabled\":true,\"actions\":{\"readFiles\":true,\"readFilesExternally\":false,\"editFiles\":false,\"editFilesExternally\":false,\"executeSafeCommands\":true,\"executeAllCommands\":false,\"useBrowser\":false,\"useMcp\":false},\"maxRequests\":20,\"enableNotifications\":false,\"favorites\":[\"enableAutoApprove\",\"readFiles\",\"editFiles\"]},\"browserSettings\":{\"viewport\":{\"width\":900,\"height\":600},\"remoteBrowserEnabled\":false,\"remoteBrowserHost\":\"http://localhost:9222\",\"chromeExecutablePath\":\"\",\"disableToolUse\":false,\"customArgs\":\"\"},\"focusChainSettings\":{\"enabled\":true,\"remindClineInterval\":6},\"focusChainFeatureFlagEnabled\":false,\"preferredLanguage\":\"English\",\"openaiReasoningEffort\":\"medium\",\"mode\":\"act\",\"strictPlanModeEnabled\":true,\"useAutoCondense\":false,\"mcpMarketplaceEnabled\":true,\"mcpDisplayMode\":\"plain\",\"telemetrySetting\":\"unset\",\"planActSeparateModelsSetting\":false,\"enableCheckpointsSetting\":true,\"distinctId\":\"test-member-789\",\"globalClineRulesToggles\":{},\"localClineRulesToggles\":{},\"localWindsurfRulesToggles\":{},\"localCursorRulesToggles\":{},\"localWorkflowToggles\":{},\"globalWorkflowToggles\":{},\"shellIntegrationTimeout\":4000,\"terminalReuseEnabled\":true,\"defaultTerminalProfile\":\"default\",\"isNewUser\":true,\"welcomeViewCompleted\":true,\"mcpResponsesCollapsed\":false,\"terminalOutputLineLimit\":500,\"taskHistory\":[],\"platform\":\"darwin\",\"shouldShowAnnouncement\":false,\"extensionInfo\":{\"name\":\"claude-dev\",\"publisher\":\"saoudrizwan\"},\"workspaceRoots\":[],\"primaryRootIndex\":0,\"isMultiRootWorkspace\":false}"}}, "duration": 0}, {"requestId": "806afa5a-ece5-48b3-87fc-2d842cd4bc5f", "service": "cline.StateService", "method": "getLatestState", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": true}, "response": {"message": {"stateJson": "{\"version\":\"3.28.0\",\"apiConfiguration\":{\"openAiHeaders\":{},\"planModeApiProvider\":\"cline\",\"planModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\",\"actModeApiProvider\":\"cline\",\"actModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\"},\"uriScheme\":\"vscode\",\"clineMessages\":[],\"currentFocusChainChecklist\":null,\"autoApprovalSettings\":{\"version\":1,\"enabled\":true,\"actions\":{\"readFiles\":true,\"readFilesExternally\":false,\"editFiles\":false,\"editFilesExternally\":false,\"executeSafeCommands\":true,\"executeAllCommands\":false,\"useBrowser\":false,\"useMcp\":false},\"maxRequests\":20,\"enableNotifications\":false,\"favorites\":[\"enableAutoApprove\",\"readFiles\",\"editFiles\"]},\"browserSettings\":{\"viewport\":{\"width\":900,\"height\":600},\"remoteBrowserEnabled\":false,\"remoteBrowserHost\":\"http://localhost:9222\",\"chromeExecutablePath\":\"\",\"disableToolUse\":false,\"customArgs\":\"\"},\"focusChainSettings\":{\"enabled\":true,\"remindClineInterval\":6},\"focusChainFeatureFlagEnabled\":false,\"preferredLanguage\":\"English\",\"openaiReasoningEffort\":\"medium\",\"mode\":\"act\",\"strictPlanModeEnabled\":true,\"useAutoCondense\":false,\"mcpMarketplaceEnabled\":true,\"mcpDisplayMode\":\"plain\",\"telemetrySetting\":\"unset\",\"planActSeparateModelsSetting\":false,\"enableCheckpointsSetting\":true,\"distinctId\":\"test-member-789\",\"globalClineRulesToggles\":{},\"localClineRulesToggles\":{},\"localWindsurfRulesToggles\":{},\"localCursorRulesToggles\":{},\"localWorkflowToggles\":{},\"globalWorkflowToggles\":{},\"shellIntegrationTimeout\":4000,\"terminalReuseEnabled\":true,\"defaultTerminalProfile\":\"default\",\"isNewUser\":true,\"welcomeViewCompleted\":true,\"mcpResponsesCollapsed\":false,\"terminalOutputLineLimit\":500,\"taskHistory\":[],\"platform\":\"darwin\",\"shouldShowAnnouncement\":false,\"extensionInfo\":{\"name\":\"claude-dev\",\"publisher\":\"saoudrizwan\"},\"workspaceRoots\":[],\"primaryRootIndex\":0,\"isMultiRootWorkspace\":false}"}}, "duration": 0}], "stats": {"totalRequests": 7, "pendingRequests": 0, "completedRequests": 7, "errorRequests": 0}}