{"startTime": "2025-09-12T17:30:49.739Z", "entries": [{"requestId": "e7b3e982-8e47-41ce-a20b-1f3b46f3108f", "service": "cline.ModelsService", "method": "updateApiConfigurationProto", "isStreaming": false, "request": {"message": {"apiConfiguration": {"openAiHeaders": {}, "openRouterApiKey": "", "planModeApiProvider": 1, "planModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "actModeApiProvider": 1, "actModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "favoritedModelIds": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "88f55caa-aeb6-4bc9-9a4d-bf7bc36633fb", "service": "cline.ModelsService", "method": "updateApiConfigurationProto", "isStreaming": false, "request": {"message": {"apiConfiguration": {"openAiHeaders": {}, "openRouterApiKey": "", "planModeApiProvider": 16, "planModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "actModeApiProvider": 16, "actModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "favoritedModelIds": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "43b27ef0-fca6-4bd9-8b09-51a16721116e", "service": "cline.ModelsService", "method": "updateApiConfigurationProto", "isStreaming": false, "request": {"message": {"apiConfiguration": {"openAiHeaders": {}, "openRouterApiKey": "", "planModeApiProvider": 1, "planModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "actModeApiProvider": 1, "actModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "favoritedModelIds": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "f64f2ea4-51ac-4b40-8866-6f95fc1e727a", "service": "cline.ModelsService", "method": "updateApiConfigurationProto", "isStreaming": false, "request": {"message": {"apiConfiguration": {"openAiHeaders": {}, "openRouterApiKey": "test-api-key", "planModeApiProvider": 1, "planModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "actModeApiProvider": 1, "actModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "favoritedModelIds": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "d333e5b6-2a09-4c8e-ba26-8a3def26e6e9", "service": "cline.StateService", "method": "setWelcomeViewCompleted", "isStreaming": false, "request": {"message": {"value": true}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "2a2de207-f6fe-4072-b058-2ff376c1852a", "service": "cline.StateService", "method": "updateTelemetrySetting", "isStreaming": false, "request": {"message": {"setting": 1}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 0}, {"requestId": "2a2de207-f6fe-4072-b058-2ff376c1852a", "service": "cline.StateService", "method": "getLatestState", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": true}, "response": {"message": {"stateJson": "{\"version\":\"3.28.0\",\"apiConfiguration\":{\"openRouterApiKey\":\"test-api-key\",\"openAiHeaders\":{},\"planModeApiProvider\":\"openrouter\",\"planModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\",\"actModeApiProvider\":\"openrouter\",\"actModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\"},\"uriScheme\":\"vscode\",\"clineMessages\":[],\"currentFocusChainChecklist\":null,\"autoApprovalSettings\":{\"version\":1,\"enabled\":true,\"actions\":{\"readFiles\":true,\"readFilesExternally\":false,\"editFiles\":false,\"editFilesExternally\":false,\"executeSafeCommands\":true,\"executeAllCommands\":false,\"useBrowser\":false,\"useMcp\":false},\"maxRequests\":20,\"enableNotifications\":false,\"favorites\":[\"enableAutoApprove\",\"readFiles\",\"editFiles\"]},\"browserSettings\":{\"viewport\":{\"width\":900,\"height\":600},\"remoteBrowserEnabled\":false,\"remoteBrowserHost\":\"http://localhost:9222\",\"chromeExecutablePath\":\"\",\"disableToolUse\":false,\"customArgs\":\"\"},\"focusChainSettings\":{\"enabled\":true,\"remindClineInterval\":6},\"focusChainFeatureFlagEnabled\":false,\"preferredLanguage\":\"English\",\"openaiReasoningEffort\":\"medium\",\"mode\":\"act\",\"strictPlanModeEnabled\":true,\"useAutoCondense\":false,\"mcpMarketplaceEnabled\":true,\"mcpDisplayMode\":\"plain\",\"telemetrySetting\":\"enabled\",\"planActSeparateModelsSetting\":false,\"enableCheckpointsSetting\":true,\"distinctId\":\"5f9fb200052a0035a677c0ea783a54ee888686c8fadcf3c7e3369580a761a587\",\"globalClineRulesToggles\":{},\"localClineRulesToggles\":{},\"localWindsurfRulesToggles\":{},\"localCursorRulesToggles\":{},\"localWorkflowToggles\":{},\"globalWorkflowToggles\":{},\"shellIntegrationTimeout\":4000,\"terminalReuseEnabled\":true,\"defaultTerminalProfile\":\"default\",\"isNewUser\":true,\"welcomeViewCompleted\":true,\"mcpResponsesCollapsed\":false,\"terminalOutputLineLimit\":500,\"taskHistory\":[],\"platform\":\"darwin\",\"shouldShowAnnouncement\":false,\"extensionInfo\":{\"name\":\"claude-dev\",\"publisher\":\"saoudrizwan\"},\"workspaceRoots\":[],\"primaryRootIndex\":0,\"isMultiRootWorkspace\":false}"}}, "duration": 0}], "stats": {"totalRequests": 7, "pendingRequests": 0, "completedRequests": 7, "errorRequests": 0}}