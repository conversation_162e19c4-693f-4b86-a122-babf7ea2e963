{"startTime": "2025-09-12T17:30:53.553Z", "entries": [{"requestId": "2513d23f-faba-4ccf-b421-69301b0bb9e2", "service": "cline.ModelsService", "method": "updateApiConfigurationProto", "isStreaming": false, "request": {"message": {"apiConfiguration": {"openAiHeaders": {}, "openRouterApiKey": "test-api-key", "planModeApiProvider": 1, "planModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "actModeApiProvider": 1, "actModeFireworksModelId": "accounts/fireworks/models/kimi-k2-instruct-0905", "favoritedModelIds": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "5ffd53cf-477c-40bd-be08-3fd8774be338", "service": "cline.StateService", "method": "setWelcomeViewCompleted", "isStreaming": false, "request": {"message": {"value": true}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 1}, {"requestId": "fea8fb5e-38a7-4efe-a867-84f18f49c872", "service": "cline.TaskService", "method": "newTask", "isStreaming": false, "request": {"message": {"text": "Hello, <PERSON><PERSON>!", "images": [], "files": []}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 55}, {"requestId": "45c1c219-c66f-4314-a518-a0119c01eb2a", "service": "cline.TaskService", "method": "clearTask", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 2}, {"requestId": "7f12797b-f8cf-4282-abb1-60efd32affdb", "service": "cline.StateService", "method": "togglePlanActModeProto", "isStreaming": false, "request": {"message": {"mode": 0, "chatContent": {"images": [], "files": []}}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {"value": false}}, "duration": 1}, {"requestId": "b2ad03f7-1ce7-4149-826d-7d69b8b206d1", "service": "cline.TaskService", "method": "newTask", "isStreaming": false, "request": {"message": {"text": "Plan mode submission", "images": [], "files": []}}, "status": "completed", "meta": {"synthetic": false}, "response": {"message": {}}, "duration": 52}, {"requestId": "b2ad03f7-1ce7-4149-826d-7d69b8b206d1", "service": "cline.StateService", "method": "getLatestState", "isStreaming": false, "request": {"message": {}}, "status": "completed", "meta": {"synthetic": true}, "response": {"message": {"stateJson": "{\"version\":\"3.28.0\",\"apiConfiguration\":{\"openRouterApiKey\":\"test-api-key\",\"openAiHeaders\":{},\"planModeApiProvider\":\"openrouter\",\"planModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\",\"actModeApiProvider\":\"openrouter\",\"actModeFireworksModelId\":\"accounts/fireworks/models/kimi-k2-instruct-0905\"},\"uriScheme\":\"vscode\",\"currentTaskItem\":{\"id\":\"*************\",\"ulid\":\"01K4ZFNTSXTMT1NB5M3STCF9C9\",\"ts\":*************,\"task\":\"Plan mode submission\",\"tokensIn\":0,\"tokensOut\":0,\"totalCost\":0,\"size\":630,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false},\"clineMessages\":[{\"ts\":*************,\"type\":\"say\",\"say\":\"text\",\"text\":\"Plan mode submission\",\"images\":[],\"files\":[],\"conversationHistoryIndex\":-1},{\"ts\":*************,\"type\":\"say\",\"say\":\"api_req_started\",\"text\":\"{\\\"request\\\":\\\"<task>\\\\nPlan mode submission\\\\n</task>\\\\n\\\\nLoading...\\\"}\",\"conversationHistoryIndex\":-1}],\"currentFocusChainChecklist\":null,\"autoApprovalSettings\":{\"version\":3,\"enabled\":true,\"actions\":{\"readFiles\":true,\"readFilesExternally\":false,\"editFiles\":false,\"editFilesExternally\":false,\"executeSafeCommands\":true,\"executeAllCommands\":false,\"useBrowser\":false,\"useMcp\":false},\"maxRequests\":20,\"enableNotifications\":false,\"favorites\":[\"enableAutoApprove\",\"readFiles\",\"editFiles\"]},\"browserSettings\":{\"viewport\":{\"width\":900,\"height\":600},\"remoteBrowserEnabled\":false,\"remoteBrowserHost\":\"http://localhost:9222\",\"chromeExecutablePath\":\"\",\"disableToolUse\":false,\"customArgs\":\"\"},\"focusChainSettings\":{\"enabled\":true,\"remindClineInterval\":6},\"focusChainFeatureFlagEnabled\":false,\"preferredLanguage\":\"English\",\"openaiReasoningEffort\":\"medium\",\"mode\":\"plan\",\"strictPlanModeEnabled\":true,\"useAutoCondense\":false,\"mcpMarketplaceEnabled\":true,\"mcpDisplayMode\":\"plain\",\"telemetrySetting\":\"unset\",\"planActSeparateModelsSetting\":false,\"enableCheckpointsSetting\":true,\"distinctId\":\"5f9fb200052a0035a677c0ea783a54ee888686c8fadcf3c7e3369580a761a587\",\"globalClineRulesToggles\":{},\"localClineRulesToggles\":{},\"localWindsurfRulesToggles\":{},\"localCursorRulesToggles\":{},\"localWorkflowToggles\":{},\"globalWorkflowToggles\":{},\"shellIntegrationTimeout\":4000,\"terminalReuseEnabled\":true,\"defaultTerminalProfile\":\"default\",\"isNewUser\":true,\"welcomeViewCompleted\":true,\"mcpResponsesCollapsed\":false,\"terminalOutputLineLimit\":500,\"taskHistory\":[{\"id\":\"*************\",\"ulid\":\"01K4ZFNTSXTMT1NB5M3STCF9C9\",\"ts\":*************,\"task\":\"Plan mode submission\",\"tokensIn\":0,\"tokensOut\":0,\"totalCost\":0,\"size\":630,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false},{\"id\":\"1757698254515\",\"ulid\":\"01K4ZFNSNKFZ8088BYYQ46EY71\",\"ts\":1757698255254,\"task\":\"Hello, Cline!\",\"tokensIn\":0,\"tokensOut\":0,\"totalCost\":0,\"size\":2652,\"cwdOnTaskInitialization\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"isFavorited\":false}],\"platform\":\"darwin\",\"shouldShowAnnouncement\":false,\"extensionInfo\":{\"name\":\"claude-dev\",\"publisher\":\"saoudrizwan\"},\"workspaceRoots\":[{\"path\":\"/Users/<USER>/Desktop/cline/cline/src/test/e2e/fixtures/workspace\",\"name\":\"workspace\",\"vcs\":\"git\",\"commitHash\":\"0ac634924409b1ac2b125e1d03be4abc91455161\"}],\"primaryRootIndex\":0,\"isMultiRootWorkspace\":false}"}}, "duration": 0}], "stats": {"totalRequests": 7, "pendingRequests": 0, "completedRequests": 7, "errorRequests": 0}}